import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:orange_ui/common/common_fun.dart';
import 'package:orange_ui/model/chat_and_live_stream/chat.dart';
import 'package:orange_ui/model/get_interest.dart';
import 'package:orange_ui/model/setting.dart';
import 'package:orange_ui/model/user/registration_user.dart';
import 'package:orange_ui/utils/firebase_res.dart';
import 'package:orange_ui/utils/pref_res.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PrefService {
  static int userId = -1;

  static Future<bool?> getLoginText() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool(PrefConst.isLogin);
  }

  static Future<void> setLoginText(bool value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool(PrefConst.isLogin, value);
  }

  static Future<bool?> getDialog(String key) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool(key);
  }

  static Future<void> setDialog(String key, bool value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool(key, value);
  }

  static Future<String?> getFullName() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString(PrefConst.fullName);
  }

  static Future<void> setFullName(String value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(PrefConst.fullName, value);
  }

  static Future<String?> getEmail() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString(PrefConst.email);
  }

  static Future<void> setLatitude(String value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(PrefConst.latitude, value);
  }

  static Future<String?> getLatitude() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString(PrefConst.latitude);
  }

  static Future<void> setLongitude(String value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(PrefConst.longitude, value);
  }

  static Future<String?> getLongitude() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString(PrefConst.longitude);
  }

  static Future<void> saveString(String key, String value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, value);
  }

  static Future<void> saveInt(String key, int value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt(key, value);
  }

  static Future<void> saveUser(RegistrationUserData? value) async {
    if (value != null) {
      userId = value.id ?? -1;
      await saveString(PrefConst.registrationUser, jsonEncode(value));
    }
  }

  static void updateFirebase() async {
    FirebaseFirestore db = FirebaseFirestore.instance;
    RegistrationUserData? registrationUserData = await getUserData();
    db
        .collection(FirebaseRes.userChatList)
        .doc('${registrationUserData?.id}')
        .collection(FirebaseRes.userList)
        .withConverter(
          fromFirestore: Conversation.fromFirestore,
          toFirestore: (Conversation value, options) {
            return value.toFirestore();
          },
        )
        .get()
        .then((value) {
          for (var element in value.docs) {
            db
                .collection(FirebaseRes.userChatList)
                .doc("${element.data().user?.userid}")
                .collection(FirebaseRes.userList)
                .doc('${registrationUserData?.id}')
                .withConverter(
                  fromFirestore: Conversation.fromFirestore,
                  toFirestore:
                      (Conversation value, options) => value.toFirestore(),
                )
                .get()
                .then((value) {
                  ChatUser? user = value.data()?.user;
                  user?.username = registrationUserData?.fullname ?? '';
                  user?.age =
                      registrationUserData?.age != null
                          ? registrationUserData?.age.toString()
                          : '';
                  user?.image = CommonFun.getProfileImage(
                    images: registrationUserData?.images,
                  );
                  user?.city = registrationUserData?.live ?? '';
                  db
                      .collection(FirebaseRes.userChatList)
                      .doc('${element.data().user?.userid}')
                      .collection(FirebaseRes.userList)
                      .doc('${registrationUserData?.id}')
                      .update({FirebaseRes.user: user?.toJson()});
                });
          }
        });
  }

  static Future<void> saveSettingData(SettingData? value) async {
    await saveString(PrefConst.settingData, jsonEncode(value));
  }

  static Future<String?> getString(String key) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString(key);
  }

  static Future<int?> getInt(String key) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getInt(key);
  }

  static Future<RegistrationUserData?> getUserData() async {
    String? data = await getString(PrefConst.registrationUser);
    if (data == null || data.isEmpty) return null;
    return RegistrationUserData.fromJson(jsonDecode(data));
  }

  static Future<GetInterest?> getInterest() async {
    String? data = await getString(PrefConst.interest);
    if (data == null || data.isEmpty) return null;
    try {
      return GetInterest.fromJson(jsonDecode(data));
    } catch (e) {
      // If JSON is malformed, clear it and return null
      print('Error parsing interest data: $e');
      await saveString(PrefConst.interest, '');
      return null;
    }
  }

  static Future<SettingData?> getSettingData() async {
    String? data = await getString(PrefConst.settingData);
    if (data == null || data.isEmpty) return null;
    return SettingData.fromJson(jsonDecode(data));
  }

  static Future<void> clearKey(String key) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }

  static Future<void> setSubscriptionStatus(
    String productId,
    bool isActive,
  ) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool('subscription_$productId', isActive);
  }

  static Future<bool> getSubscriptionStatus(String productId) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool('subscription_$productId') ?? false;
  }

  static Future<void> clearAllSubscriptions() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final keys = prefs.getKeys().where(
      (key) => key.startsWith('subscription_'),
    );
    for (String key in keys) {
      await prefs.remove(key);
    }
  }

  // Save list of image paths
  static Future<void> saveImagePaths(List<String> paths) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(PrefConst.imagePaths, paths);
  }

  // Get list of image paths
  static Future<List<String>?> getImagePaths() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(PrefConst.imagePaths);
  }

  // Save profile data locally
  static Future<void> saveProfileData({
    required String bio,
    required String age,
    required String gender,
    required String latitude,
    required String longitude,
    required List<String> hobbies,
  }) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(PrefConst.bio, bio);
    await prefs.setString(PrefConst.age, age);
    await prefs.setString(PrefConst.gender, gender);
    await prefs.setString(PrefConst.latitude, latitude);
    await prefs.setString(PrefConst.longitude, longitude);
    await prefs.setStringList(PrefConst.hobbies, hobbies);
  }

  // Save search filters
  static Future<void> saveSearchFilters(String filtersJson) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(PrefConst.searchFilters, filtersJson);
  }

  // Get search filters
  static Future<String?> getSearchFilters() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString(PrefConst.searchFilters);
  }
}
