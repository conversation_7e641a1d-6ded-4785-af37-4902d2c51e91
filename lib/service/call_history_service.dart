import 'dart:math';
import 'package:orange_ui/model/call/call_data.dart';
import 'package:orange_ui/service/pref_service.dart';

class CallHistoryService {
  static final CallHistoryService _instance = CallHistoryService._internal();
  factory CallHistoryService() => _instance;
  CallHistoryService._internal();

  static const List<Map<String, String>> _dummyUsers = [
    {
      'id': '101',
      'name': '<PERSON>',
      'image': 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
    },
    {
      'id': '102', 
      'name': '<PERSON>',
      'image': 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',
    },
    {
      'id': '103',
      'name': '<PERSON>', 
      'image': 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150',
    },
    {
      'id': '104',
      'name': '<PERSON>',
      'image': 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150',
    },
    {
      'id': '105',
      'name': '<PERSON>',
      'image': 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=150',
    },
    {
      'id': '106',
      'name': 'Emma Wilson',
      'image': 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
    },
    {
      'id': '107',
      'name': 'Olivia Taylor',
      'image': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
    },
    {
      'id': '108',
      'name': 'Sophia Miller',
      'image': 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=150',
    },
  ];

  Future<List<CallData>> generateDummyCallHistory() async {
    final userData = await PrefService.getUserData();
    if (userData?.id == null) return [];

    final List<CallData> dummyCalls = [];
    final random = Random();
    final now = DateTime.now();

    // Generate 15-20 dummy calls
    final callCount = 15 + random.nextInt(6);

    for (int i = 0; i < callCount; i++) {
      final dummyUser = _dummyUsers[random.nextInt(_dummyUsers.length)];
      final isIncoming = random.nextBool();
      final callType = random.nextInt(2); // 0 = audio, 1 = video
      final callStatus = _generateCallStatus(random);
      
      // Generate realistic timestamps (last 30 days)
      final daysAgo = random.nextInt(30);
      final hoursAgo = random.nextInt(24);
      final minutesAgo = random.nextInt(60);
      final startTime = now.subtract(Duration(
        days: daysAgo,
        hours: hoursAgo,
        minutes: minutesAgo,
      ));

      // Generate call duration based on status
      int? duration;
      DateTime? endTime;
      if (callStatus == CallStatus.answered || callStatus == CallStatus.ended) {
        duration = _generateCallDuration(random);
        endTime = startTime.add(Duration(seconds: duration));
      }

      final callData = CallData(
        callId: '${DateTime.now().millisecondsSinceEpoch}_$i',
        callerId: isIncoming ? dummyUser['id'] : '${userData.id}',
        callerName: isIncoming ? dummyUser['name'] : userData.fullname,
        callerImage: isIncoming ? dummyUser['image'] : _getUserImage(userData),
        receiverId: isIncoming ? '${userData.id}' : dummyUser['id'],
        receiverName: isIncoming ? userData.fullname : dummyUser['name'],
        receiverImage: isIncoming ? _getUserImage(userData) : dummyUser['image'],
        channelId: '${startTime.millisecondsSinceEpoch}',
        agoraToken: '',
        callType: callType,
        callStatus: callStatus,
        startTime: startTime,
        endTime: endTime,
        duration: duration,
        isIncoming: isIncoming,
      );

      dummyCalls.add(callData);
    }

    // Sort by start time (most recent first)
    dummyCalls.sort((a, b) {
      if (a.startTime == null && b.startTime == null) return 0;
      if (a.startTime == null) return 1;
      if (b.startTime == null) return -1;
      return b.startTime!.compareTo(a.startTime!);
    });

    return dummyCalls;
  }

  int _generateCallStatus(Random random) {
    // Weight the probabilities for more realistic call history
    final statusWeights = [
      CallStatus.answered, // 40%
      CallStatus.answered,
      CallStatus.answered,
      CallStatus.answered,
      CallStatus.ended,     // 20%
      CallStatus.ended,
      CallStatus.missed,    // 25%
      CallStatus.missed,
      CallStatus.missed,
      CallStatus.rejected,  // 15%
      CallStatus.rejected,
    ];
    
    return statusWeights[random.nextInt(statusWeights.length)];
  }

  int _generateCallDuration(Random random) {
    // Generate realistic call durations
    final durationTypes = [
      () => 30 + random.nextInt(90),      // Short calls: 30s - 2min
      () => 120 + random.nextInt(300),    // Medium calls: 2min - 7min  
      () => 420 + random.nextInt(600),    // Long calls: 7min - 17min
      () => 1020 + random.nextInt(1800),  // Very long calls: 17min - 47min
    ];
    
    final weights = [5, 3, 2, 1]; // Favor shorter calls
    final totalWeight = weights.reduce((a, b) => a + b);
    final randomWeight = random.nextInt(totalWeight);
    
    int currentWeight = 0;
    for (int i = 0; i < weights.length; i++) {
      currentWeight += weights[i];
      if (randomWeight < currentWeight) {
        return durationTypes[i]();
      }
    }
    
    return durationTypes[0](); // Fallback
  }

  String? _getUserImage(dynamic userData) {
    if (userData?.images != null && userData.images.isNotEmpty) {
      return userData.images[0].image;
    }
    return 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150';
  }
}
