import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:orange_ui/common/common_ui.dart';
import 'package:orange_ui/generated/l10n.dart';
import 'package:orange_ui/model/get_interest.dart';
import 'package:orange_ui/model/user/registration_user.dart';
import 'package:orange_ui/screen/starting_profile_screen/starting_profile_screen.dart';
import 'package:orange_ui/screen/webview_screen/webview_screen.dart';
import 'package:orange_ui/service/pref_service.dart';
import 'package:orange_ui/utils/urls.dart';
import 'package:stacked/stacked.dart';
import 'package:uuid/uuid.dart';

class RegisterScreenViewModel extends BaseViewModel {
  TextEditingController fullNameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController pwdController = TextEditingController();
  TextEditingController confirmPwdController = TextEditingController();

  FocusNode fullNameFocus = FocusNode();
  FocusNode emailFocus = FocusNode();
  FocusNode pwdFocus = FocusNode();
  FocusNode confirmPwdFocus = FocusNode();

  String fullNameError = '';
  String emailError = '';
  String pwdError = '';
  String confirmPwdError = '';

  bool showPwd = false;

  void onViewTap() {
    showPwd = !showPwd;
    notifyListeners();
  }

  void onTermsOfUseTap() {
    Get.to(
      () => WebViewScreen(
        appBarTitle: S.current.termsOfUse,
        url: Urls.aTermsOfUse,
      ),
    );
  }

  void onPrivacyPolicyTap() {
    Get.to(
      () => WebViewScreen(
        appBarTitle: S.current.privacyPolicy,
        url: Urls.aPrivacyPolicy,
      ),
    );
  }

  Future<void> onContinueTap() async {
    if (isValid()) {
      try {
        CommonUI.lottieLoader();
        // Generate a unique id using uuid
        final uuid = Uuid();
        final dummyUserData = RegistrationUserData(
          id: uuid.v4().hashCode,
          fullname: fullNameController.text,
          password: pwdController.text,
          identity: emailController.text,
          // Add other required fields with dummy values
        );
        // Save dummy interests if not already present
        var interests = await PrefService.getInterest();
        if (interests == null ||
            interests.data == null ||
            interests.data!.isEmpty) {
          final dummyInterests = [
            Interest(id: 1, title: 'Movies', image: ''),
            Interest(id: 2, title: 'Traveling', image: ''),
            Interest(id: 3, title: 'Cooking', image: ''),
            Interest(id: 4, title: 'Sports', image: ''),
            Interest(id: 5, title: 'Music', image: ''),
          ];
          final interestData = GetInterest(
            status: true,
            message: "Dummy interests",
            data: dummyInterests,
          );
          await PrefService.saveString(
            'interest',
            jsonEncode(interestData.toJson()),
          );
        }
        await PrefService.saveUser(dummyUserData);
        PrefService.userId = dummyUserData.id ?? -1;
        // Save selected interests after registration
        await saveSelectedInterests(
          [],
        ); // Pass the actual selected interests list here

        Get.back(); // Close the loading dialog
        CommonUI.snackBarWidget(S.current.registrationSuccessfully);
        Get.off(() => StartingProfileScreen(userData: dummyUserData));
      } catch (e) {
        Get.back(); // Close the loading dialog
        CommonUI.snackBarWidget('Registration failed: ${e.toString()}');
        print('Registration error: $e');
      }
    }
    notifyListeners();
  }

  Future<void> saveSelectedInterests(List<int> selectedInterestIds) async {
    // Save the selected interest IDs as a comma-separated string in user data
    RegistrationUserData? user = await PrefService.getUserData();
    if (user != null) {
      // Convert the list of interest IDs to a comma-separated string
      String interestIdsString = selectedInterestIds.join(',');
      // Update the user's interests field
      user = RegistrationUserData(
        id: user.id,
        fullname: user.fullname,
        password: user.password,
        identity: user.identity,
        interests: interestIdsString,
        // Copy other fields as needed
        age: user.age,
        gender: user.gender,
        bio: user.bio,
        live: user.live,
        latitude: user.latitude,
        longitude: user.longitude,
        images: user.images,
      );
      await PrefService.saveUser(user);
    }
  }

  bool isValid() {
    int count = 0;
    if (emailController.text == '') {
      count++;
      emailFocus.requestFocus();
      emailError = S.current.enterEmail;
      return false;
    } else {
      bool isEmailValid = GetUtils.isEmail(emailController.text);
      if (!isEmailValid) {
        CommonUI.snackBarWidget(S.current.pleaseValidEmail);
        return false;
      } else {
        emailFocus.unfocus();
        FocusScope.of(Get.context!).requestFocus(fullNameFocus);
      }
    }
    if (fullNameController.text == '') {
      count++;
      fullNameFocus.requestFocus();
      fullNameError = S.current.enterFullName;
      return false;
    } else {
      fullNameFocus.unfocus();
      FocusScope.of(Get.context!).requestFocus(pwdFocus);
    }
    if (pwdController.text == '') {
      count++;
      pwdError = S.current.enterPassword;
      pwdFocus.requestFocus();
      return false;
    } else {
      pwdFocus.unfocus();
      FocusScope.of(Get.context!).requestFocus(confirmPwdFocus);
    }
    if (confirmPwdController.text == '') {
      count++;
      confirmPwdError = S.current.enterConfirmPassword;
      confirmPwdFocus.requestFocus();
      return false;
    } else {
      if (confirmPwdController.text != pwdController.text) {
        count++;
        confirmPwdController.clear();
        confirmPwdError = S.current.passwordMismatch;
      }
      confirmPwdFocus.unfocus();
    }
    return count == 0 ? true : false;
  }

  @override
  void dispose() {
    fullNameFocus.dispose();
    emailFocus.dispose();
    pwdFocus.dispose();
    confirmPwdFocus.dispose();
    fullNameController.dispose();
    emailController.dispose();
    pwdController.dispose();
    confirmPwdController.dispose();
    super.dispose();
  }
}
