import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:orange_ui/common/common_ui.dart';
import 'package:orange_ui/model/call/call_data.dart';
import 'package:orange_ui/screen/audio_call_screen/audio_call_screen.dart';
import 'package:orange_ui/screen/video_call_screen/video_call_screen.dart';
import 'package:orange_ui/service/pref_service.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:orange_ui/utils/firebase_res.dart';
import 'package:stacked/stacked.dart';

class CallHistoryScreenViewModel extends BaseViewModel {
  FirebaseFirestore db = FirebaseFirestore.instance;
  StreamSubscription? _callHistorySubscription;

  List<CallData> callHistory = [];
  bool isLoading = false;

  void init() {
    lo();
  }

  // void getCallHistory() {
  //   isLoading = true;
  //   notifyListeners();

  //   PrefService.getUserData().then((userData) {
  //     if (userData?.id != null) {
  //       _callHistorySubscription = db
  //           .collection(FirebaseRes.calls)
  //           .where('callerId', isEqualTo: '${userData!.id}')
  //           .orderBy('startTime', descending: true)
  //           .limit(50)
  //           .withConverter(
  //             fromFirestore: CallData.fromFirestore,
  //             toFirestore: (CallData value, options) => value.toFirestore(),
  //           )
  //           .snapshots()
  //           .listen((callerSnapshot) {
  //             // Also get calls where user was the receiver
  //             db
  //                 .collection(FirebaseRes.calls)
  //                 .where('receiverId', isEqualTo: '${userData.id}')
  //                 .orderBy('startTime', descending: true)
  //                 .limit(50)
  //                 .withConverter(
  //                   fromFirestore: CallData.fromFirestore,
  //                   toFirestore:
  //                       (CallData value, options) => value.toFirestore(),
  //                 )
  //                 .snapshots()
  //                 .listen((receiverSnapshot) {
  //                   // Combine both lists
  //                   List<CallData> allCalls = [];

  //                   // Add calls where user was caller
  //                   for (var doc in callerSnapshot.docs) {
  //                     CallData call = doc.data();
  //                     call.isIncoming = false;
  //                     allCalls.add(call);
  //                   }

  //                   // Add calls where user was receiver
  //                   for (var doc in receiverSnapshot.docs) {
  //                     CallData call = doc.data();
  //                     call.isIncoming = true;
  //                     allCalls.add(call);
  //                   }

  //                   // Sort by start time (most recent first)
  //                   allCalls.sort((a, b) {
  //                     if (a.startTime == null && b.startTime == null) return 0;
  //                     if (a.startTime == null) return 1;
  //                     if (b.startTime == null) return -1;
  //                     return b.startTime!.compareTo(a.startTime!);
  //                   });

  //                   callHistory = allCalls;

  //                   // If no real calls exist, load dummy data
  //                   if (callHistory.isEmpty) {
  //                     callHistory = _getDummyCallHistory();
  //                   }

  //                   isLoading = false;
  //                   notifyListeners();
  //                 });
  //           });
  //     } else {
  //       isLoading = false;
  //       notifyListeners();
  //     }
  //   });
  // }

  List<CallData> getDummyCallHistory() {
    final now = DateTime.now();
    return [
      CallData(
        callId: 'dummy_1',
        callerId: '101',
        callerName: 'Sarah Johnson',
        callerImage:
            'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        receiverId: 'current_user',
        receiverName: 'You',
        receiverImage: '',
        channelId: 'channel_1',
        agoraToken: '',
        callType: CallType.video,
        callStatus: CallStatus.answered,
        startTime: now.subtract(const Duration(hours: 2)),
        endTime: now.subtract(const Duration(hours: 2, minutes: -15)),
        duration: 900, // 15 minutes
        isIncoming: true,
      ),
      CallData(
        callId: 'dummy_2',
        callerId: 'current_user',
        callerName: 'You',
        callerImage: '',
        receiverId: '102',
        receiverName: 'Jessica Brown',
        receiverImage:
            'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',
        channelId: 'channel_2',
        agoraToken: '',
        callType: CallType.audio,
        callStatus: CallStatus.missed,
        startTime: now.subtract(const Duration(hours: 5)),
        endTime: null,
        duration: null,
        isIncoming: false,
      ),
      CallData(
        callId: 'dummy_3',
        callerId: '103',
        callerName: 'Ashley Davis',
        callerImage:
            'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150',
        receiverId: 'current_user',
        receiverName: 'You',
        receiverImage: '',
        channelId: 'channel_3',
        agoraToken: '',
        callType: CallType.video,
        callStatus: CallStatus.ended,
        startTime: now.subtract(const Duration(days: 1)),
        endTime: now.subtract(const Duration(days: 1, minutes: -8)),
        duration: 480, // 8 minutes
        isIncoming: true,
      ),
      CallData(
        callId: 'dummy_4',
        callerId: 'current_user',
        callerName: 'You',
        callerImage: '',
        receiverId: '104',
        receiverName: 'Maria Garcia',
        receiverImage:
            'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150',
        channelId: 'channel_4',
        agoraToken: '',
        callType: CallType.audio,
        callStatus: CallStatus.answered,
        startTime: now.subtract(const Duration(days: 2)),
        endTime: now.subtract(const Duration(days: 2, minutes: -25)),
        duration: 1500, // 25 minutes
        isIncoming: false,
      ),
      CallData(
        callId: 'dummy_5',
        callerId: '105',
        callerName: 'Lisa Anderson',
        callerImage:
            'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=150',
        receiverId: 'current_user',
        receiverName: 'You',
        receiverImage: '',
        channelId: 'channel_5',
        agoraToken: '',
        callType: CallType.video,
        callStatus: CallStatus.rejected,
        startTime: now.subtract(const Duration(days: 3)),
        endTime: null,
        duration: null,
        isIncoming: true,
      ),
    ];
  }

  void onCallHistoryTap(CallData callData) {
    // Show call details or options
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: ColorRes.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(
                callData.isVideoCall ? Icons.videocam : Icons.phone,
                color: ColorRes.darkBlue,
              ),
              title: Text('Make ${callData.callTypeString}'),
              onTap: () {
                Get.back();
                onMakeCallTap(callData);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: ColorRes.red),
              title: const Text('Delete'),
              onTap: () {
                Get.back();
                _deleteCallHistory(callData);
              },
            ),
          ],
        ),
      ),
    );
  }

  void onMakeCallTap(CallData callData) {
    // Create a new call with the same contact
    final newCallData = CallData(
      callId: DateTime.now().millisecondsSinceEpoch.toString(),
      callerId:
          callData.isIncoming == true ? callData.receiverId : callData.callerId,
      callerName:
          callData.isIncoming == true
              ? callData.receiverName
              : callData.callerName,
      callerImage:
          callData.isIncoming == true
              ? callData.receiverImage
              : callData.callerImage,
      receiverId:
          callData.isIncoming == true ? callData.callerId : callData.receiverId,
      receiverName:
          callData.isIncoming == true
              ? callData.callerName
              : callData.receiverName,
      receiverImage:
          callData.isIncoming == true
              ? callData.callerImage
              : callData.receiverImage,
      channelId: DateTime.now().millisecondsSinceEpoch.toString(),
      agoraToken: '', // This should be generated from backend
      callType: callData.callType,
      callStatus: CallStatus.calling,
      startTime: DateTime.now(),
      isIncoming: false,
    );

    // Navigate to appropriate call screen
    if (callData.isVideoCall) {
      Get.to(() => VideoCallScreen(callData: newCallData));
    } else {
      Get.to(() => AudioCallScreen(callData: newCallData));
    }
  }

  void _deleteCallHistory(CallData callData) {
    // Check if this is dummy data (callId contains underscore for dummy data)
    if (callData.callId?.contains('_') == true) {
      // Handle dummy data deletion locally
      callHistory.removeWhere((call) => call.callId == callData.callId);
      notifyListeners();
      CommonUI.snackBar(message: 'Call deleted from history');
      return;
    }

    // Handle real Firebase data deletion
    CommonUI.lottieLoader();

    db
        .collection(FirebaseRes.calls)
        .doc(callData.callId)
        .delete()
        .then((_) {
          Get.back();
          CommonUI.snackBar(message: 'Call deleted from history');
        })
        .catchError((error) {
          Get.back();
          CommonUI.snackBar(message: 'Failed to delete call');
        });
  }

  void onBackBtnTap() {
    Get.back();
  }

  // Public method to manually load dummy data for testing
  void loadDummyData() {
    callHistory = getDummyCallHistory();
    notifyListeners();
  }

  @override
  void dispose() {
    _callHistorySubscription?.cancel();
    super.dispose();
  }
}
