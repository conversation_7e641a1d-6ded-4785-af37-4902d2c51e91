import 'package:flutter/material.dart';
import 'package:orange_ui/common/top_bar_area.dart';
import 'package:orange_ui/screen/call_history_screen/call_history_screen_view_model.dart';
import 'package:orange_ui/screen/call_history_screen/widgets/call_history_item.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:stacked/stacked.dart';

class CallHistoryScreen extends StatelessWidget {
  const CallHistoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<CallHistoryScreenViewModel>.reactive(
      onViewModelReady: (model) {
        model.init();
      },
      viewModelBuilder: () => CallHistoryScreenViewModel(),
      builder: (context, model, child) {
        return Scaffold(
          backgroundColor: ColorRes.white,
          body: Column(
            children: [
              const TopBarArea(title: 'Call History'),
              Expanded(
                child:
                    model.isLoading
                        ? const Center(
                          child: CircularProgressIndicator(
                            color: ColorRes.darkBlue,
                          ),
                        )
                        : model.callHistory.isEmpty
                        ? _buildEmptyState(model)
                        : ListView.builder(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          itemCount: model.callHistory.length,
                          itemBuilder: (context, index) {
                            final call = model.callHistory[index];
                            return CallHistoryItem(
                              callData: call,
                              onTap: () => model.onCallHistoryTap(call),
                              onCallTap: () => model.onMakeCallTap(call),
                            );
                          },
                        ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(CallHistoryScreenViewModel model) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.call,
            size: 80,
            color: ColorRes.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 20),
          Text(
            'No call history',
            style: TextStyle(
              fontSize: 18,
              color: ColorRes.grey.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            'Your call history will appear here',
            style: TextStyle(
              fontSize: 14,
              color: ColorRes.grey.withValues(alpha: 0.5),
            ),
          ),
          const SizedBox(height: 30),
          ElevatedButton(
            onPressed: () => model.loadDummyData(),
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorRes.darkBlue,
              foregroundColor: ColorRes.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Load Sample Call History'),
          ),
        ],
      ),
    );
  }
}
