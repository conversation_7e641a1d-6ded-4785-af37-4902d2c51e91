                        -H/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Projects/wattlesol/Orange/android/app/build/intermediates/cxx/RelWithDebInfo/x2q3m399/obj/x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Projects/wattlesol/Orange/android/app/build/intermediates/cxx/RelWithDebInfo/x2q3m399/obj/x86_64
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-B/Users/<USER>/Projects/wattlesol/Orange/android/app/.cxx/RelWithDebInfo/x2q3m399/x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2